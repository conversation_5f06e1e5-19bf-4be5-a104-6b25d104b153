package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

class DeadlockDetectorTest {

    @Test
    void aucun_deadlock_detecte() {
        assertFalse(DeadlockDetector.hasDeadlocks());
        assertFalse(DeadlockDetector.checkAndLogDeadlocks());
        assertFalse(DeadlockDetector.checkAndHandleDeadlocks());
        assertEquals("No deadlocks detected", DeadlockDetector.getDeadlockStatus());
    }

    @Test
    void format_statut_deadlock_est_correct() {
        String status = DeadlockDetector.getDeadlockStatus();
        assertNotNull(status);
        assertFalse(status.isEmpty());

        assertTrue(status.equals("No deadlocks detected") || status.startsWith("DEADLOCK:"));
    }

    @Test
    void situation_de_deadlock_est_detectee() throws InterruptedException {
        // Create two locks that will cause a deadlock
        final Object lock1 = new Object();
        final Object lock2 = new Object();
        final CountDownLatch latch = new CountDownLatch(2);

        // Thread 1: acquires lock1, then tries to acquire lock2
        Thread thread1 = new Thread(() -> {
            synchronized (lock1) {
                latch.countDown();
                try {
                    // Wait for thread2 to acquire lock2
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock2) {
                    // This will never be reached due to deadlock
                }
            }
        }, "DeadlockTest-Thread1");

        // Thread 2: acquires lock2, then tries to acquire lock1
        Thread thread2 = new Thread(() -> {
            synchronized (lock2) {
                latch.countDown();
                try {
                    // Wait for thread1 to acquire lock1
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock1) {
                    // This will never be reached due to deadlock
                }
            }
        }, "DeadlockTest-Thread2");

        // Start both threads
        thread1.start();
        thread2.start();

        // Wait for both threads to acquire their first locks
        assertTrue(latch.await(5, TimeUnit.SECONDS));

        // Give some time for the deadlock to occur
        Thread.sleep(500);

        // Check for deadlocks
        assertTrue(DeadlockDetector.hasDeadlocks(), "Deadlock should be detected");
        assertTrue(DeadlockDetector.checkAndLogDeadlocks(), "checkAndLogDeadlocks should return true");
        // Note: We don't test checkAndHandleDeadlocks() here because it calls System.exit(1)

        String status = DeadlockDetector.getDeadlockStatus();
        assertTrue(status.contains("DEADLOCK"), "Status should indicate deadlock: " + status);
        assertTrue(status.contains("threads deadlocked"), "Status should mention deadlocked threads: " + status);

        // Clean up - interrupt the threads to break the deadlock
        thread1.interrupt();
        thread2.interrupt();

        // Wait for threads to finish
        thread1.join(1000);
        thread2.join(1000);
    }

    @Test
    void hasDeadlocks_retourne_false_quand_aucun_deadlock() {
        // Test multiple calls to ensure consistency
        assertFalse(DeadlockDetector.hasDeadlocks());
        assertFalse(DeadlockDetector.hasDeadlocks());
        assertFalse(DeadlockDetector.hasDeadlocks());
    }

    @Test
    void getDeadlockStatus_format_correct_sans_deadlock() {
        String status = DeadlockDetector.getDeadlockStatus();
        assertEquals("No deadlocks detected", status);
    }

    @Test
    void getDeadlockStatus_format_correct_avec_deadlock() throws InterruptedException {
        // Create a deadlock scenario
        final Object lock1 = new Object();
        final Object lock2 = new Object();
        final CountDownLatch latch = new CountDownLatch(2);

        Thread thread1 = new Thread(() -> {
            synchronized (lock1) {
                latch.countDown();
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock2) {
                    // Never reached
                }
            }
        }, "DeadlockStatusTest-Thread1");

        Thread thread2 = new Thread(() -> {
            synchronized (lock2) {
                latch.countDown();
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock1) {
                    // Never reached
                }
            }
        }, "DeadlockStatusTest-Thread2");

        thread1.start();
        thread2.start();

        assertTrue(latch.await(5, TimeUnit.SECONDS));
        Thread.sleep(500); // Allow deadlock to form

        String status = DeadlockDetector.getDeadlockStatus();
        assertTrue(status.startsWith("DEADLOCK:"), "Status should start with DEADLOCK:");
        assertTrue(status.contains("threads deadlocked"), "Status should mention deadlocked threads");

        // Clean up
        thread1.interrupt();
        thread2.interrupt();
        thread1.join(1000);
        thread2.join(1000);
    }

    @Test
    void checkAndLogDeadlocks_retourne_false_sans_deadlock() {
        // When no deadlocks exist, should return false
        assertFalse(DeadlockDetector.checkAndLogDeadlocks());
    }

    @Test
    void checkAndHandleDeadlocks_retourne_false_sans_deadlock() {
        // When no deadlocks exist, should return false and not exit
        assertFalse(DeadlockDetector.checkAndHandleDeadlocks());
    }

    @Test
    @Timeout(10)
    void multiple_calls_consistent_behavior() {
        // Test that multiple rapid calls don't cause issues
        for (int i = 0; i < 100; i++) {
            assertFalse(DeadlockDetector.hasDeadlocks());
            assertEquals("No deadlocks detected", DeadlockDetector.getDeadlockStatus());
            assertFalse(DeadlockDetector.checkAndLogDeadlocks());
            assertFalse(DeadlockDetector.checkAndHandleDeadlocks());
        }
    }

    @Test
    void threadMXBean_integration_test() {
        // Test that we're using the ThreadMXBean correctly
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        assertNotNull(threadBean, "ThreadMXBean should be available");

        // Test that findDeadlockedThreads works (should return null when no deadlocks)
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        assertTrue(deadlockedThreads == null || deadlockedThreads.length == 0,
                   "Should have no deadlocked threads initially");
    }

    @Test
    void edge_case_empty_thread_info() {
        // This tests the robustness of the code when ThreadInfo might be null
        // (which can happen in some JVM implementations)

        // We can't easily mock this, but we can test that our current implementation
        // handles the normal case correctly
        assertFalse(DeadlockDetector.hasDeadlocks());
        String status = DeadlockDetector.getDeadlockStatus();
        assertNotNull(status);
        assertFalse(status.isEmpty());
    }

    @Test
    @Timeout(15)
    void deadlock_detection_timing_test() throws InterruptedException {
        // Test that deadlock detection works within reasonable time bounds
        final Object lock1 = new Object();
        final Object lock2 = new Object();
        final CountDownLatch setupLatch = new CountDownLatch(2);
        final CountDownLatch detectionLatch = new CountDownLatch(1);

        Thread thread1 = new Thread(() -> {
            synchronized (lock1) {
                setupLatch.countDown();
                try {
                    detectionLatch.await(10, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock2) {
                    // Never reached
                }
            }
        }, "TimingTest-Thread1");

        Thread thread2 = new Thread(() -> {
            synchronized (lock2) {
                setupLatch.countDown();
                try {
                    detectionLatch.await(10, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock1) {
                    // Never reached
                }
            }
        }, "TimingTest-Thread2");

        thread1.start();
        thread2.start();

        // Wait for both threads to acquire their first locks
        assertTrue(setupLatch.await(5, TimeUnit.SECONDS));

        // Now let them try to acquire the second locks (creating deadlock)
        detectionLatch.countDown();

        // Give some time for deadlock to form
        Thread.sleep(200);

        // Test detection timing
        long startTime = System.currentTimeMillis();
        boolean hasDeadlocks = DeadlockDetector.hasDeadlocks();
        long detectionTime = System.currentTimeMillis() - startTime;

        assertTrue(hasDeadlocks, "Should detect deadlock");
        assertTrue(detectionTime < 1000, "Detection should be fast (< 1s), was: " + detectionTime + "ms");

        // Clean up
        thread1.interrupt();
        thread2.interrupt();
        thread1.join(1000);
        thread2.join(1000);
    }
}
