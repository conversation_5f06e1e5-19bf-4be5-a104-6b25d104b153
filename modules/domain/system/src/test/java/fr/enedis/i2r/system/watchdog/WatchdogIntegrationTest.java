package fr.enedis.i2r.system.watchdog;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.lang.reflect.Field;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

/**
 * Integration tests for ThreadWatchdog and DeadlockDetector components
 * working together as they would in a real application.
 */
class WatchdogIntegrationTest {

    @BeforeEach
    void setUp() {
        ThreadWatchdog.clearAll();
    }

    @AfterEach
    void tearDown() {
        ThreadWatchdog.clearAll();
    }

    @Test
    @Timeout(10)
    void threadWatchdog_et_deadlockDetector_fonctionnent_ensemble() throws Exception {
        // Test that both components can work together without interference
        String threadName = "integration-test-thread";
        ThreadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        // Register thread with watchdog
        ThreadWatchdog.register(threadName);

        // Check that deadlock detector works while watchdog is running
        assertFalse(DeadlockDetector.hasDeadlocks());
        assertEquals("No deadlocks detected", DeadlockDetector.getDeadlockStatus());
        assertFalse(DeadlockDetector.checkAndLogDeadlocks());

        // Wait for some heartbeats
        Thread.sleep(500);

        // Verify thread is still being monitored
        Field threadsField = ThreadWatchdog.class.getDeclaredField("threads");
        threadsField.setAccessible(true);
        ConcurrentMap<String, Instant> threads = (ConcurrentMap<String, Instant>) threadsField.get(null);
        assertTrue(threads.containsKey(threadName));

        // Deadlock detection should still work
        assertFalse(DeadlockDetector.hasDeadlocks());

        ThreadWatchdog.unregister(threadName);
    }

    @Test
    @Timeout(15)
    void multiple_threads_avec_deadlock_detection() throws Exception {
        // Test multiple threads being monitored while deadlock detection runs
        ThreadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        String[] threadNames = {"integration-thread-1", "integration-thread-2", "integration-thread-3"};

        // Register multiple threads
        for (String threadName : threadNames) {
            ThreadWatchdog.register(threadName);
        }

        // Run deadlock detection multiple times while threads are being monitored
        for (int i = 0; i < 10; i++) {
            assertFalse(DeadlockDetector.hasDeadlocks());
            Thread.sleep(100);
        }

        // Verify all threads are still registered
        Field threadsField = ThreadWatchdog.class.getDeclaredField("threads");
        threadsField.setAccessible(true);
        ConcurrentMap<String, Instant> threads = (ConcurrentMap<String, Instant>) threadsField.get(null);

        for (String threadName : threadNames) {
            assertTrue(threads.containsKey(threadName), "Thread should still be registered: " + threadName);
        }

        // Unregister all threads
        for (String threadName : threadNames) {
            ThreadWatchdog.unregister(threadName);
        }

        assertEquals(0, threads.size(), "All threads should be unregistered");
    }

    @Test
    @Timeout(20)
    void deadlock_detection_pendant_thread_monitoring() throws Exception {
        // Test deadlock detection while ThreadWatchdog is actively monitoring threads
        ThreadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        // Register some threads for monitoring
        ThreadWatchdog.register("monitor-thread-1");
        ThreadWatchdog.register("monitor-thread-2");

        // Create a deadlock scenario
        final Object lock1 = new Object();
        final Object lock2 = new Object();
        final CountDownLatch latch = new CountDownLatch(2);

        Thread deadlockThread1 = new Thread(() -> {
            synchronized (lock1) {
                latch.countDown();
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock2) {
                    // Never reached
                }
            }
        }, "DeadlockIntegration-Thread1");

        Thread deadlockThread2 = new Thread(() -> {
            synchronized (lock2) {
                latch.countDown();
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock1) {
                    // Never reached
                }
            }
        }, "DeadlockIntegration-Thread2");

        deadlockThread1.start();
        deadlockThread2.start();

        assertTrue(latch.await(5, TimeUnit.SECONDS));
        Thread.sleep(500); // Allow deadlock to form

        // Deadlock detection should work even while ThreadWatchdog is running
        assertTrue(DeadlockDetector.hasDeadlocks(), "Should detect deadlock");
        String status = DeadlockDetector.getDeadlockStatus();
        assertTrue(status.contains("DEADLOCK"), "Status should indicate deadlock");

        // ThreadWatchdog should still be functioning
        Field threadsField = ThreadWatchdog.class.getDeclaredField("threads");
        threadsField.setAccessible(true);
        ConcurrentMap<String, Instant> threads = (ConcurrentMap<String, Instant>) threadsField.get(null);
        assertEquals(2, threads.size(), "Monitored threads should still be registered");

        // Clean up deadlocked threads
        deadlockThread1.interrupt();
        deadlockThread2.interrupt();
        deadlockThread1.join(1000);
        deadlockThread2.join(1000);

        // Clean up monitored threads
        ThreadWatchdog.unregister("monitor-thread-1");
        ThreadWatchdog.unregister("monitor-thread-2");
    }

    @Test
    @Timeout(10)
    void stress_test_concurrent_operations() throws Exception {
        // Stress test with concurrent operations on both components
        ThreadWatchdog.setHeartbeatInterval(Duration.ofMillis(500));

        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch doneLatch = new CountDownLatch(3);

        // Thread 1: Continuously register/unregister threads
        Thread watchdogThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 50; i++) {
                    String threadName = "stress-thread-" + i;
                    ThreadWatchdog.register(threadName);
                    Thread.sleep(10);
                    ThreadWatchdog.unregister(threadName);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        // Thread 2: Continuously check for deadlocks
        Thread deadlockThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 100; i++) {
                    DeadlockDetector.hasDeadlocks();
                    DeadlockDetector.getDeadlockStatus();
                    Thread.sleep(5);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        // Thread 3: Mixed operations
        Thread mixedThread = new Thread(() -> {
            try {
                startLatch.await();
                for (int i = 0; i < 30; i++) {
                    ThreadWatchdog.register("mixed-thread-" + i);
                    DeadlockDetector.checkAndLogDeadlocks();
                    Thread.sleep(15);
                    ThreadWatchdog.unregister("mixed-thread-" + i);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                doneLatch.countDown();
            }
        });

        watchdogThread.start();
        deadlockThread.start();
        mixedThread.start();

        // Start all operations
        startLatch.countDown();

        // Wait for completion
        assertTrue(doneLatch.await(8, TimeUnit.SECONDS), "All stress test threads should complete");

        // Verify clean state
        Field threadsField = ThreadWatchdog.class.getDeclaredField("threads");
        threadsField.setAccessible(true);
        ConcurrentMap<String, Instant> threads = (ConcurrentMap<String, Instant>) threadsField.get(null);
        assertEquals(0, threads.size(), "No threads should remain registered after stress test");

        // Verify deadlock detection still works
        assertFalse(DeadlockDetector.hasDeadlocks());
    }

    @Test
    @Timeout(10)
    void configuration_changes_pendant_monitoring() throws Exception {
        // Test changing configuration while monitoring is active
        String threadName = "config-change-thread";

        // Start with one interval
        ThreadWatchdog.setHeartbeatInterval(Duration.ofSeconds(2));
        ThreadWatchdog.register(threadName);

        Thread.sleep(1000); // Wait for initial heartbeat

        // Change interval
        ThreadWatchdog.setHeartbeatInterval(Duration.ofSeconds(1));

        // Register another thread with new interval
        ThreadWatchdog.register("config-change-thread-2");

        Thread.sleep(1500); // Wait for heartbeats with new interval

        // Verify both threads are still monitored
        Field threadsField = ThreadWatchdog.class.getDeclaredField("threads");
        threadsField.setAccessible(true);
        ConcurrentMap<String, Instant> threads = (ConcurrentMap<String, Instant>) threadsField.get(null);
        assertEquals(2, threads.size(), "Both threads should be monitored");

        // Deadlock detection should work throughout
        assertFalse(DeadlockDetector.hasDeadlocks());

        ThreadWatchdog.unregister(threadName);
        ThreadWatchdog.unregister("config-change-thread-2");
    }
}
