package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ThreadWatchdog {

    private static final Logger logger = LoggerFactory.getLogger(ThreadWatchdog.class);

    private static final ConcurrentMap<String, Instant> threads = new ConcurrentHashMap<>();
    private static final ConcurrentMap<String, ScheduledFuture<?>> scheduledHeartbeats = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);
    private static Duration heartbeatInterval = Duration.ofSeconds(45);
    private static final Duration THREAD_TIMEOUT = heartbeatInterval.multipliedBy(2);
    private static ScheduledFuture<?> timeoutChecker;

    public static void setHeartbeatInterval(Duration interval) {
        heartbeatInterval = interval;
    }

    public static void register(String threadName) {
        threads.put(threadName, Instant.now());
        startHeartbeat(threadName);
        startTimeoutCheckerIfNeeded();
        logger.info("Thread registered for monitoring: {}", threadName);
    }

    private static synchronized void startTimeoutCheckerIfNeeded() {
        if (timeoutChecker == null || timeoutChecker.isCancelled()) {
            timeoutChecker = scheduler.scheduleAtFixedRate(
                ThreadWatchdog::checkForDeadThreads,
                THREAD_TIMEOUT.toSeconds(),
                THREAD_TIMEOUT.toSeconds() / 2, // Check every 30 seconds
                TimeUnit.SECONDS
            );
            logger.info("Thread timeout checker started with {}s timeout", THREAD_TIMEOUT.toSeconds());
        }
    }

    private static void checkForDeadThreads() {
        Instant now = Instant.now();
        threads.entrySet().removeIf(entry -> {
            String threadName = entry.getKey();
            Instant lastHeartbeat = entry.getValue();
            Duration timeSinceLastHeartbeat = Duration.between(lastHeartbeat, now);

            if (timeSinceLastHeartbeat.compareTo(THREAD_TIMEOUT) > 0) {
                logger.error("DEAD THREAD DETECTED: {} - Last heartbeat was {}s ago (timeout: {}s)",
                    threadName, timeSinceLastHeartbeat.toSeconds(), THREAD_TIMEOUT.toSeconds());

                // Cancel the heartbeat scheduler for this dead thread
                ScheduledFuture<?> future = scheduledHeartbeats.remove(threadName);
                if (future != null) {
                    future.cancel(false);
                    logger.info("Heartbeat scheduler cancelled for dead thread: {}", threadName);
                }

                return true; // Remove from threads map
            }
            return false; // Keep in threads map
        });
    }

    private static void startHeartbeat(String threadName) {
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
            () -> CompletableFuture.runAsync(() -> heartbeat(threadName)),
            heartbeatInterval.toSeconds(),
            heartbeatInterval.toSeconds(),
            TimeUnit.SECONDS
        );
        scheduledHeartbeats.put(threadName, future);
        logger.info("Heartbeat started for thread: {} with interval: {}s", threadName, heartbeatInterval.toSeconds());
    }

    private static void heartbeat(String threadName) {
        Instant lastHeartbeat = threads.get(threadName);
        if (lastHeartbeat != null) {
            threads.put(threadName, Instant.now());
            logger.debug("Heartbeat recorded for thread: {} at {}", threadName, Instant.now());
        } else {
            logger.warn("Heartbeat for unregistered thread: {}", threadName);
        }
    }

    public static void unregister(String threadName) {
        Instant removed = threads.remove(threadName);
        ScheduledFuture<?> future = scheduledHeartbeats.remove(threadName);
        if (future != null) {
            future.cancel(false);
            logger.info("Heartbeat stopped for thread: {}", threadName);
        }
        if (removed != null) {
            logger.info("Thread unregistered from monitoring: {}", threadName);
        }
    }

    public static void clearAll() {
        threads.clear();
        scheduledHeartbeats.values().forEach(future -> future.cancel(false));
        scheduledHeartbeats.clear();

        // Stop timeout checker if running
        if (timeoutChecker != null && !timeoutChecker.isCancelled()) {
            timeoutChecker.cancel(false);
            timeoutChecker = null;
            logger.debug("Timeout checker stopped");
        }

        logger.debug("All threads cleared from monitoring");
    }

    public static void shutdown() {
        clearAll();
        scheduler.shutdown();
        logger.info("ThreadWatchdog scheduler shutdown");
    }
}
