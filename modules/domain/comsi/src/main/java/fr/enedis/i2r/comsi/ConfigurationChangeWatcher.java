package fr.enedis.i2r.comsi;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
public class ConfigurationChangeWatcher implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);

    private DatabaseUpdateWatcherPort databaseUpdateWatcherPort;
    private SiConfigurationNotifierPort siNotifierPort;
    private volatile boolean running = true;

    public ConfigurationChangeWatcher(DatabaseUpdateWatcherPort databaseUpdatePort, SiConfigurationNotifierPort siNotifierPort) {
        this.databaseUpdateWatcherPort = databaseUpdatePort;
        this.siNotifierPort = siNotifierPort;
    }

    @Override
    public void run() {
        try {
            ThreadWatchdog.register(ConfigurationChangeWatcher.class.getSimpleName());
            logger.info("Configuration change watcher started");

            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    List<ConfigurationValue> updatedValues = this.databaseUpdateWatcherPort
                        .waitForUpdates()
                        .stream().filter(confValue -> confValue.parameter().watched)
                        .toList();

                    for (ConfigurationValue updatedValue: updatedValues) {
                        this.notifySi(updatedValue);
                    }
                } catch (InterruptedException e) {
                    logger.info("Configuration change watcher interrupted, stopping...");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Throwable e) {
                    logger.error("erreur lors de l'écoute d'un changement de configuration", e);
                    // Continue running after error
                }
            }
        } finally {
            ThreadWatchdog.unregister(ConfigurationChangeWatcher.class.getSimpleName());
            logger.info("Configuration change watcher stopped");
        }
    }

    public void stop() {
        logger.info("Stopping configuration change watcher...");
        running = false;
    }

    private void notifySi(ConfigurationValue updatedConfigurationValue) throws Exception {
        switch (updatedConfigurationValue.parameter()) {
            case BipState -> this.handleStateChange(updatedConfigurationValue.value());
        }
    }

    private void handleStateChange(String newState) throws NumberFormatException, Exception {
        Integer stateValue = Integer.parseInt(newState);

        BipStatus bipStatus = BipStatus.fromStatusCode(stateValue)
            .orElseThrow(() -> new Exception(String.format("nouvel état du boitier invalide: %d", stateValue)));

        this.siNotifierPort.notifyStateChange(bipStatus);
    }
}
