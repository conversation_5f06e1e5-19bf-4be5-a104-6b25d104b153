package fr.enedis.i2r.comsi;

import java.time.Clock;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;

public class ComSI implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ComSI.class);

    private ComSiConfiguration comSiConfiguration;
    private ComsiParametersPort parametersPort;
    private SiClientPort siClientPort;
    private BoardManagerPort boardManagerPort;
    private ModuleSecuritePort moduleSecuritePort;
    private ModemManagerPort modemManagerPort;
    private DatabaseUpdateWatcherPort dbUpdateWatcherPort;
    private Clock clock;

    public ComSI(ComSiConfiguration comSiConfiguration,
                 ComsiParametersPort parametersPort,
                 SiClientPort siClientPort,
                 BoardManagerPort boardManagerPort,
                 ModuleSecuritePort moduleSecuritePort,
                 ModemManagerPort modemManager,
                 DatabaseUpdateWatcherPort dbUpdateWatcherPort,
                 Clock clock) {

        this.comSiConfiguration = comSiConfiguration;
        this.parametersPort = parametersPort;
        this.siClientPort = siClientPort;
        this.boardManagerPort = boardManagerPort;
        this.moduleSecuritePort = moduleSecuritePort;
        this.modemManagerPort = modemManager;
        this.dbUpdateWatcherPort = dbUpdateWatcherPort;
        this.clock = clock;
    }

    @Override
    public void run() {
        try {
            ThreadWatchdog.register(ComSI.class.getSimpleName());
            logger.info("Running ComSi...");

            if (comSiConfiguration.bipStatus() == BipStatus.INIT) {
                logger.debug("Sending configuration to iCOM");

                ConfigurationBoitier config = ConfigurationBoitier.from(
                    comSiConfiguration,
                    clock.instant(), // TODO stubbing dates for now, replace by real values
                    parametersPort.getConfigurationHash(),
                    boardManagerPort.getAds(),
                    moduleSecuritePort.getIdms(),
                    modemManagerPort.getIccid()
                );
                siClientPort.sendConfigurationBoitier(config);
            }

            ConfigurationChangeWatcher configurationChangeWatcher = new ConfigurationChangeWatcher(dbUpdateWatcherPort, siClientPort.getSiConfigurationNotifier());
            ExecutorService executorService = Executors.newFixedThreadPool(1);
            executorService.submit(configurationChangeWatcher);

            logger.info("ComSI en cours de fonctionnement");
            while (true) {
                Thread.sleep(1000);
            }
        } catch (Throwable e) {
            logger.error("Erreur pendant le démarrage du module iCOM", e);
        } finally {
            ThreadWatchdog.unregister(ComSI.class.getSimpleName());
        }
    }

}
