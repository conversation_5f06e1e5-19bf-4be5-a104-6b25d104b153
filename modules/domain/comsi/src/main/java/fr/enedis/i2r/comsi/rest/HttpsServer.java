package fr.enedis.i2r.comsi.rest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.errors.rest.ComsiServerException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.rest.controllers.ConfigurationController;
import fr.enedis.i2r.comsi.rest.controllers.LoggingController;
import fr.enedis.i2r.comsi.rest.controllers.MonitoringController;
import fr.enedis.i2r.comsi.rest.controllers.StatutBoitierController;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.system.LoggingLoader;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
import io.javalin.Javalin;
import io.javalin.community.ssl.SslPlugin;
import io.javalin.config.JavalinConfig;
import io.javalin.http.HttpStatus;
import io.javalin.openapi.plugin.OpenApiPlugin;
import io.javalin.openapi.plugin.swagger.SwaggerPlugin;

public class HttpsServer implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(HttpsServer.class);
    public static final Integer INSECURE_PORT = 8081;
    public static final Integer SECURE_PORT = 443;

    private BipStatusManager bipStatusManager;
    private ComSiConfiguration comSiConfiguration;
    private ShellExecutorPort shellExecutorPort;
    private LoggingLoader loggingLoader;
    private SslPlugin sslPlugin;
    private ComsiParametersPort parametersPort;
    private BoardManagerPort boardManagerPort;
    private ModuleSecuritePort moduleSecuritePort;
    private ModemManagerPort modemManagerPort;
    private SiClientPort siClientPort;

    public HttpsServer(BipStatusManager bipStatusManager, ComSiConfiguration comSiConfiguration,
            ShellExecutorPort shellExecutorPort, LoggingLoader loggingLoader, SslPlugin sslPlugin,
            ComsiParametersPort parametersPort, BoardManagerPort boardManagerPort,
            ModuleSecuritePort moduleSecuritePort, ModemManagerPort modemManagerPort, SiClientPort siClientPort) {
        this.bipStatusManager = bipStatusManager;
        this.comSiConfiguration = comSiConfiguration;
        this.shellExecutorPort = shellExecutorPort;
        this.loggingLoader = loggingLoader;
        this.sslPlugin = sslPlugin;
        this.parametersPort = parametersPort;
        this.boardManagerPort = boardManagerPort;
        this.moduleSecuritePort = moduleSecuritePort;
        this.modemManagerPort = modemManagerPort;
        this.siClientPort = siClientPort;
    }

    @Override
    public void run() {
        try {
            ThreadWatchdog.register(HttpsServer.class.getSimpleName());
            logger.debug("Démarrage du serveur web...");

            Javalin app = Javalin.create(this::configurePlugins);

        StatutBoitierController activationBoitierHandler = new StatutBoitierController(bipStatusManager);
        MonitoringController monitoringController = new MonitoringController(comSiConfiguration, shellExecutorPort);
        LoggingController loggingController = new LoggingController(this.loggingLoader);
        ConfigurationController configurationController = new ConfigurationController(comSiConfiguration,
            parametersPort, boardManagerPort, moduleSecuritePort, modemManagerPort, siClientPort);

        app.get(RestEndpoints.API_ROOT + RestEndpoints.TIME_SYNC, monitoringController::getTimeSync);
        app.post(RestEndpoints.API_ROOT + RestEndpoints.LOG_APP, loggingController::changeLogLevel);
        app.get(RestEndpoints.DB_CFG, configurationController::getConfiguration);

        app.put(RestEndpoints.STATUS_CHANGE, activationBoitierHandler::changeStatus);

        app.exception(ComsiServerException.class, (e, ctx) -> {
            logger.error("Erreur dans ComSi", e);
            ctx.status(e.getStatusCode());
            ctx.result(String.format("erreur lors de votre requête à ComSI:\n%s", e.getMessage()));
        });
        app.exception(Exception.class, (e, ctx) -> {
            logger.error("Erreur technique du bip",e);
            ctx.status(HttpStatus.INTERNAL_SERVER_ERROR);
            ctx.result(String.format("erreur lors de votre requête à ComSI:\n%s", e.getMessage()));
        });

            try {
                app.start();
                logger.debug("REST Server has started and is running...");
                logger.info("Swagger docs at http://localhost:" + INSECURE_PORT + "/swagger");
                logger.info("OpenAPI JSON at http://localhost:" + INSECURE_PORT + "/openapi");
                logger.info("Swagger docs at https://localhost:" + SECURE_PORT + "/swagger");
                logger.info("OpenAPI JSON at https://localhost:" + SECURE_PORT + "/openapi");
            } catch (Exception e) {
                logger.error("Fail to start javalin", e);
            }
        } finally {
            ThreadWatchdog.unregister(HttpsServer.class.getSimpleName());
        }
    }

    private void configurePlugins(JavalinConfig config) {
        config.registerPlugin(new OpenApiPlugin(pluginConfig -> {
            pluginConfig.withDefinitionConfiguration((version, definition) -> {
                definition.withInfo(info -> {
                    info.setTitle("OpenAPI i2r");
                    info.setVersion("1.0.0");
                    info.setDescription("Documentation de l'API i2R");
                });
            });
        }));
        config.registerPlugin(new SwaggerPlugin());
        config.showJavalinBanner = false;
        config.registerPlugin(this.sslPlugin);
    }
}
